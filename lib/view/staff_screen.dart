import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:insight/l10n/app_localizations.dart';
import '../widgets/bottom_bar.dart';
import 'employee_profile_screen.dart';
import 'report_screen.dart';
import 'admin_dashboard.dart';
import 'user_settings_screen.dart';

class StaffScreen extends StatefulWidget {
  final String userRole;

  const StaffScreen({Key? key, required this.userRole}) : super(key: key);

  @override
  State<StaffScreen> createState() => _StaffScreenState();
}

class _StaffScreenState extends State<StaffScreen> {
  int _currentIndex = 2; // Staff tab is selected
  final TextEditingController _searchController = TextEditingController();

  // Sample staff data
  List<StaffMember> get _staffMembers => [
    StaffMember(
      name: '<PERSON><PERSON><PERSON> <PERSON>',
      role: AppLocalizations.of(context)?.serviceCounter ?? 'Service Counter',
      score: 92,
      avatar: 'assets/sk.png',
    ),
    Staff<PERSON><PERSON>ber(
      name: '<PERSON>',
      role: AppLocalizations.of(context)?.serviceCounter ?? 'Service Counter',
      score: 78,
      avatar: 'assets/jane.png',
    ),
    StaffMember(
      name: '<PERSON>',
      role: AppLocalizations.of(context)?.serviceArea ?? 'Service area',
      score: 45,
      avatar: 'assets/sarah.png',
    ),
  ];

  void _onItemTapped(int index) {
    if (index == 0) {
      // Dashboard tab
      Navigator.pushReplacement(
        context,
        PageRouteBuilder(
          pageBuilder: (context, animation, secondaryAnimation) =>
              AdminDashboard(userRole: widget.userRole),
          transitionDuration: Duration.zero,
          reverseTransitionDuration: Duration.zero,
        ),
      );
    } else if (index == 1) {
      // Reports tab
      Navigator.push(
        context,
        PageRouteBuilder(
          pageBuilder: (context, animation, secondaryAnimation) =>
              ReportScreen(userRole: widget.userRole),
          transitionDuration: Duration.zero,
          reverseTransitionDuration: Duration.zero,
        ),
      );
    } else if (index == 3) {
      // Users tab
      Navigator.pushReplacement(
        context,
        PageRouteBuilder(
          pageBuilder: (context, animation, secondaryAnimation) =>
              UserSettingsScreen(userRole: widget.userRole),
          transitionDuration: Duration.zero,
          reverseTransitionDuration: Duration.zero,
        ),
      );
    }
  }

  Color _getScoreColor(int score) {
    if (score >= 85) {
      return const Color(0xFF065F46); // Green
    } else if (score >= 60) {
      return const Color(0xFF92400E); // Yellow/Orange
    } else {
      return const Color(0xFF991B1B); // Red
    }
  }

  Color _getScoreBackgroundColor(int score) {
    if (score >= 85) {
      return const Color(0xFFD1FAE5); // Light green
    } else if (score >= 60) {
      return const Color(0xFFFEF3C7); // Light yellow
    } else {
      return const Color(0xFFFEE2E2); // Light red
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      backgroundColor: const Color(0xFFF9FAFB),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        toolbarHeight: kToolbarHeight + 35,
        automaticallyImplyLeading: false,
        centerTitle: false,
        shape: const Border(
          bottom: BorderSide(color: Color(0xFFE5E7EB), width: 1),
        ),
        title: Text(
          localizations?.staff ?? 'Staff',
          style: TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // Search Bar
            Container(
              decoration: BoxDecoration(
                color: const Color(0xFFFFFFFF),
                borderRadius: BorderRadius.circular(12.0),
                border: Border.all(color: const Color(0xFFE5E7EB), width: 1),
              ),
              child: TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: localizations?.searchStaff ?? 'Search Staff..',
                  hintStyle: const TextStyle(
                    color: Color(0xFF9CA3AF),
                    fontSize: 16,
                  ),
                  prefixIcon: const Icon(
                    Icons.search,
                    color: Color(0xFF9CA3AF),
                  ),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Filter Buttons Row
            Row(
              children: [
                // All Departments Dropdown
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 12,
                    ),
                    decoration: BoxDecoration(
                      color: const Color(0xFFFFFFFF),
                      borderRadius: BorderRadius.circular(8.0),
                      border: Border.all(
                        color: const Color(0xFFE5E7EB),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          localizations?.allDepartments ?? 'All Departments',
                          style: TextStyle(
                            color: Colors.black,
                            fontSize: 16,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                        const Icon(
                          Icons.keyboard_arrow_down,
                          size: 20,
                          color: Colors.black,
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(width: 12),

                // Score Filter Dropdown
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 12,
                    ),
                    decoration: BoxDecoration(
                      color: const Color(0xFFFFFFFF),
                      borderRadius: BorderRadius.circular(8.0),
                      border: Border.all(
                        color: const Color(0xFFE5E7EB),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          localizations?.scoreAll ?? 'Score: All',
                          style: TextStyle(
                            color: Colors.black,
                            fontSize: 16,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                        const Icon(
                          Icons.keyboard_arrow_down,
                          size: 20,
                          color: Colors.black,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // Staff List
            Expanded(
              child: ListView.builder(
                itemCount: _staffMembers.length,
                itemBuilder: (context, index) {
                  final staff = _staffMembers[index];
                  return Container(
                    margin: const EdgeInsets.only(bottom: 16),
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.withValues(alpha: 0.1),
                          spreadRadius: 1,
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: GestureDetector(
                      onTap: () {
                        // Navigate to employee profile screen
                        Navigator.push(
                          context,
                          PageRouteBuilder(
                            pageBuilder:
                                (context, animation, secondaryAnimation) =>
                                    EmployeeProfileScreen(
                                      staffMember: staff,
                                      userRole: widget.userRole,
                                    ),
                            transitionDuration: Duration.zero,
                            reverseTransitionDuration: Duration.zero,
                          ),
                        );
                      },
                      child: Row(
                        children: [
                          // Profile Picture
                          CircleAvatar(
                            radius: 24,
                            backgroundColor: const Color(0xFFE5E7EB),
                            backgroundImage: staff.avatar.isNotEmpty
                                ? AssetImage(staff.avatar)
                                : null,
                            child: staff.avatar.isEmpty
                                ? Text(
                                    staff.name
                                        .split(' ')
                                        .map(
                                          (name) =>
                                              name.isNotEmpty ? name[0] : '',
                                        )
                                        .join('')
                                        .toUpperCase(),
                                    style: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                      color: Color(0xFF6B7280),
                                    ),
                                  )
                                : null,
                          ),
                          const SizedBox(width: 16),

                          // Name and Role
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  staff.name,
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.black,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  staff.getTranslatedRole(context),
                                  style: const TextStyle(
                                    fontSize: 14,
                                    color: Color(0xFF6B7280),
                                  ),
                                ),
                              ],
                            ),
                          ),

                          // Score Badge
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 6,
                            ),
                            decoration: BoxDecoration(
                              color: _getScoreBackgroundColor(staff.score),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              '${staff.score}%',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                                color: _getScoreColor(staff.score),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: BottomBar(
        currentIndex: _currentIndex,
        onTap: _onItemTapped,
        userRole: widget.userRole,
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}

class StaffMember {
  final String name;
  final String role;
  final int score;
  final String avatar;

  StaffMember({
    required this.name,
    required this.role,
    required this.score,
    required this.avatar,
  });

  // Helper method to get translated role
  String getTranslatedRole(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    switch (role.toLowerCase()) {
      case 'service counter':
        return localizations?.serviceCounter ?? role;
      case 'service area':
        return localizations?.serviceArea ?? role;
      default:
        return role;
    }
  }
}
