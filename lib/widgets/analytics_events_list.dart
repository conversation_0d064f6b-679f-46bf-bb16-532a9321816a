import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:insight/l10n/app_localizations.dart';

class AnalyticsEventsList extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                localizations.events,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF374151),
                ),
              ),
              SvgPicture.asset(
                'assets/3dots.svg',
                width: 20,
                height: 20,
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildEventRow(localizations.incident, '4 ${localizations.events}'),
          const SizedBox(height: 8),
          _buildEventRow(localizations.behaviour, '2 ${localizations.events}'),
          const SizedBox(height: 8),
          _buildEventRow(localizations.awayTime, '1 ${localizations.event}'),
        ],
      ),
    );
  }

  Widget _buildEventRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: const TextStyle(fontSize: 15, color: Colors.black),
        ),
        Text(
          value,
          style: const TextStyle(fontSize: 15, color: Color(0xFF1F2937)),
        ),
      ],
    );
  }
} 