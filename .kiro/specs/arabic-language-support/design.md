# Design Document

## Overview

This design implements Arabic language support for the Insight Flutter application using Flutter's built-in internationalization (i18n) framework. The solution provides a seamless language switching experience with proper RTL support and persistent language preferences.

## Architecture

### Localization Framework
- **Flutter Localizations**: Utilize `flutter_localizations` and `intl` packages already included in pubspec.yaml
- **App Localization Delegate**: Custom `AppLocalizations` class to manage translations
- **Locale Management**: `LocaleProvider` using Provider pattern for state management
- **Persistence Layer**: SharedPreferences for storing language preferences

### Language Support
- **Default Language**: English (en)
- **Additional Language**: Arabic (ar)
- **Text Direction**: Automatic RTL/LTR handling based on locale
- **Fallback**: English as fallback for missing translations

## Components and Interfaces

### 1. AppLocalizations Class
```dart
class AppLocalizations {
  static AppLocalizations? of(BuildContext context);
  static const LocalizationsDelegate<AppLocalizations> delegate;
  
  // Localized strings
  String get welcomeAdmin;
  String get welcomeSupervisor;
  String get welcomeAuditor;
  String get editProfile;
  String get logOut;
  String get privacyAndSecurity;
  // ... other strings
}
```

### 2. LocaleProvider Class
```dart
class LocaleProvider extends ChangeNotifier {
  Locale _locale = const Locale('en');
  
  Locale get locale => _locale;
  
  Future<void> setLocale(Locale locale);
  Future<void> loadSavedLocale();
}
```

### 3. Language Switcher Widget
```dart
class LanguageSwitcherButton extends StatelessWidget {
  // Toggle button that switches between English and Arabic
  // Shows current non-selected language as the switch option
}
```

### 4. Main App Configuration
```dart
class MyApp extends StatelessWidget {
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => LocaleProvider(),
      child: Consumer<LocaleProvider>(
        builder: (context, localeProvider, child) {
          return MaterialApp(
            locale: localeProvider.locale,
            localizationsDelegates: [
              AppLocalizations.delegate,
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            supportedLocales: [
              Locale('en'),
              Locale('ar'),
            ],
            // ... rest of app configuration
          );
        },
      ),
    );
  }
}
```

## Data Models

### Locale Preference Storage
```dart
class LocalePreferences {
  static const String _localeKey = 'selected_locale';
  
  static Future<void> saveLocale(String languageCode);
  static Future<String?> getSavedLocale();
}
```

### Translation Keys Structure
```
{
  "en": {
    "welcome_admin": "Welcome, Admin",
    "welcome_supervisor": "Welcome, Supervisor",
    "welcome_auditor": "Welcome, Auditor",
    "edit_profile": "Edit Profile",
    "log_out": "Log Out",
    "privacy_and_security": "Privacy and Security",
    "privacy_settings": "Privacy Settings",
    "security_settings": "Security Settings",
    "two_factor_authentication": "Two-Factor Authentication",
    "language": "Language"
  },
  "ar": {
    "welcome_admin": "مرحباً، المدير",
    "welcome_supervisor": "مرحباً، المشرف", 
    "welcome_auditor": "مرحباً، المدقق",
    "edit_profile": "تعديل الملف الشخصي",
    "log_out": "تسجيل الخروج",
    "privacy_and_security": "الخصوصية والأمان",
    "privacy_settings": "إعدادات الخصوصية",
    "security_settings": "إعدادات الأمان",
    "two_factor_authentication": "المصادقة الثنائية",
    "language": "اللغة"
  }
}
```

## Error Handling

### Missing Translation Handling
- **Fallback Strategy**: Return English text if Arabic translation is missing
- **Debug Mode**: Log missing translation keys for development
- **Graceful Degradation**: App continues to function with mixed languages if needed

### Locale Loading Errors
- **SharedPreferences Failure**: Default to English if preference loading fails
- **Invalid Locale**: Validate locale codes and fallback to English for invalid codes
- **Provider Errors**: Catch and handle ChangeNotifier exceptions

## Testing Strategy

### Unit Tests
- **AppLocalizations**: Test translation retrieval for both languages
- **LocaleProvider**: Test locale switching and persistence
- **LocalePreferences**: Test saving and loading locale preferences

### Widget Tests
- **Language Switcher**: Test button appearance and tap behavior
- **Localized Widgets**: Test that widgets display correct translations
- **RTL Layout**: Test that Arabic text displays with proper RTL direction

### Integration Tests
- **Language Switching Flow**: Test complete language switching user journey
- **Persistence**: Test that language preference survives app restart
- **UI Updates**: Test that entire interface updates when language changes

## Implementation Considerations

### RTL Support
- **Automatic Direction**: Flutter automatically handles RTL for Arabic locale
- **Custom Widgets**: Ensure custom widgets respect text direction
- **Icon Positioning**: Icons and arrows should flip appropriately in RTL

### Performance
- **Lazy Loading**: Load translations only when needed
- **Caching**: Cache frequently used translations
- **Memory Management**: Dispose providers properly to prevent memory leaks

### Accessibility
- **Screen Readers**: Ensure translations work with accessibility tools
- **Font Support**: Verify Arabic fonts render correctly on all platforms
- **Semantic Labels**: Provide proper semantic labels for language switcher

### Platform Considerations
- **iOS**: Test Arabic text rendering on iOS devices
- **Android**: Verify RTL support across different Android versions
- **Web**: Ensure web version handles Arabic text and RTL properly